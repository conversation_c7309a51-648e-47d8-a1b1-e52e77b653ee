--文件来源：assets/storagebox/storagebox.xml
local storageboxAutoGen = Class("storageboxAutoGen",ClassList["MiniUICommonNodeClass"])

local storageboxCtrl,storageboxModel,storageboxView = GetInst("MiniUIManager"):GetMVC("storageboxAutoGen")

--初始化
function storageboxAutoGen:init(param)
	if self.firstInit == nil then 
		--实例化MVC
		self.ctrl = GetInst("MiniUIManager"):InstMVC("storageboxAutoGen",{incomingParam = param,root = self.rootNode,uiType = UIType.FGUI})
		--注册UI事件
		self.ctrl:RegisterUIEvents()
		--启动
		self.ctrl:Start()
	else
		--重新赋值
		if param then 
			self.ctrl.model:SetIncomingParam(param)
		end
	end
	self.firstInit = 0
end

--显示
function storageboxAutoGen:onShow()
	self.ctrl:Refresh()

	local playermain_ctrl = GetInst("MiniUIManager"):GetCtrl("playermain")
	if playermain_ctrl then
		playermain_ctrl:ShowPackOnly()
	end
end

--隐藏
function storageboxAutoGen:onHide()
	self.ctrl:Reset()

	local playermain_ctrl = GetInst("MiniUIManager"):GetCtrl("playermain")
	if playermain_ctrl then
		playermain_ctrl:HidePackOnly()
	end
end

--移除
function storageboxAutoGen:onRemove()
	self.ctrl:Remove()
	--销毁MVC实例
	GetInst("MiniUIManager"):UnInstMVC(self.ctrl)

	local playermain_ctrl = GetInst("MiniUIManager"):GetCtrl("playermain")
	if playermain_ctrl then
		playermain_ctrl:HidePackOnly()
	end
end

--Ctrl:注册UI事件
function storageboxCtrl:RegisterUIEvents()
	GetInst("MiniUIEventDispatcher"):addEventListener(self.view.widgets.mainpanelBaglist, UIEventType_ClickItem, function(obj, context)
 		if self.MainpanelBaglistClickItem then
			self:MainpanelBaglistClickItem(obj, context)
		end
	end)
	GetInst("MiniUIComponents"):setCallback(self.view.widgets.mainpanelBaglist, "GList.itemRenderer", function(comp, idx, obj)
 		if self.MainpanelBaglistItemRenderer then
			self:MainpanelBaglistItemRenderer(comp, idx, obj)
		end
	end)
end

--View:获取需要操作的节点
function storageboxView:GetHandleNodes()
	self.widgets={}
	self.widgets.mainpanel = self.root:getChild("mainpanel")
	--list
	self.widgets.mainpanelBaglist = self.widgets.mainpanel:getChild("baglist")
	self.widgets.mainpanelBaglist:getScrollPane():setDisEnableTouch()

	self.widgets.title = self.widgets.mainpanel:getChild("title")

	self.itemuis = {}
	self:RefreshItemUIList()
end

